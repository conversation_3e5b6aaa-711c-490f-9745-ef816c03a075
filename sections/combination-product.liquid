{%- render 'section-combination-product' -%}

{% schema %}
{
  "name": "Combination Product",
  "class": "combination-product",

  "settings": [
    {
      "type": "select",
      "id": "heading_size",
      "label": "t:labels.heading_size",
      "default": "h2",
      "options": [
        {
          "value": "h3",
          "label": "t:labels.sizes.small"
        },
        {
          "value": "h2",
          "label": "t:labels.sizes.medium"
        },
        {
          "value": "h1",
          "label": "t:labels.sizes.large"
        }
      ]
    },
    {
      "type": "select",
      "id": "heading_position",
      "label": "t:labels.heading_position",
      "default": "left",
      "options": [
        {
          "value": "left",
          "label": "t:labels.alignments.left"
        },
        {
          "value": "center",
          "label": "t:labels.alignments.center"
        },
        {
          "value": "right",
          "label": "t:labels.alignments.right"
        }
      ]
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "t:labels.image",
      "info": "t:info.recommended_a_square_image"
    },
    {
      "type": "image_picker",
      "id": "image_mobile",
      "label": "Mobile Image",
      "info": "Optional mobile-specific image. If not set, desktop image will be used."
    },
    {
      "type": "checkbox",
      "label": "t:labels.full_page_width",
      "id": "indent_image",
      "default": false
    },
    {
      "type": "select",
      "id": "image_position",
      "label": "t:labels.image_position",
      "default": "left",
      "options": [
        {
          "value": "left",
          "label": "t:labels.alignments.left"
        },
        {
          "value": "right",
          "label": "t:labels.alignments.right"
        }
      ]
    },
    {
      "type": "select",
      "id": "hotspot_style",
      "label": "t:labels.hot_spot_icon",
      "default": "bag",
      "options": [
        {
          "value": "dot",
          "label": "t:labels.dot"
        },
        {
          "value": "plus",
          "label": "t:labels.plus"
        },
        {
          "value": "tag",
          "label": "t:labels.tag"
        },
        {
          "value": "bag",
          "label": "t:labels.bag"
        }
      ]
    },
    {
      "type": "color",
      "id": "hotspot_color",
      "label": "t:labels.hotspot_color",
      "default": "#000000"
    }
  ],

  "presets": [
    {
      "name": "Combination Product"
    }
  ],
  "disabled_on": {
    "groups": ["footer", "header"]
  }
}
{% endschema %}
